# 针对沙盒环境的多种解决方案

# 方案1: 避免map()函数的版本
def time_to_seconds_v1(time_str):
    """避免使用map()函数"""
    if not time_str:
        return 0
    
    parts = time_str.split(':')
    if len(parts) != 3:
        return 0
    
    # 逐个转换，避免map()
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = int(parts[2])
    
    return hours * 3600 + minutes * 60 + seconds

# 方案2: 使用字典存储中间结果
def time_to_seconds_v2(time_str):
    """使用字典避免直接变量赋值"""
    if not time_str:
        return 0
    
    parts = time_str.split(':')
    if len(parts) != 3:
        return 0
    
    time_dict = {}
    time_dict.update({'h': int(parts[0])})
    time_dict.update({'m': int(parts[1])})
    time_dict.update({'s': int(parts[2])})
    
    return time_dict['h'] * 3600 + time_dict['m'] * 60 + time_dict['s']

# 方案3: 完全手工解析
def time_to_seconds_v3(time_str):
    """完全手工解析，避免所有可能的限制"""
    if not time_str:
        return 0
    
    # 查找冒号位置
    colon_positions = []
    for i in range(len(time_str)):
        if time_str[i] == ':':
            colon_positions.append(i)
    
    if len(colon_positions) != 2:
        return 0
    
    # 提取各部分
    hour_str = time_str[0:colon_positions[0]]
    minute_str = time_str[colon_positions[0]+1:colon_positions[1]]
    second_str = time_str[colon_positions[1]+1:]
    
    # 手工转换为数字
    hour_val = 0
    for char in hour_str:
        if '0' <= char <= '9':
            hour_val = hour_val * 10 + ord(char) - ord('0')
    
    minute_val = 0
    for char in minute_str:
        if '0' <= char <= '9':
            minute_val = minute_val * 10 + ord(char) - ord('0')
    
    second_val = 0
    for char in second_str:
        if '0' <= char <= '9':
            second_val = second_val * 10 + ord(char) - ord('0')
    
    return hour_val * 3600 + minute_val * 60 + second_val

# 方案4: 使用列表操作
def time_to_seconds_v4(time_str):
    """使用列表操作避免直接赋值"""
    if not time_str:
        return 0
    
    parts = time_str.split(':')
    if len(parts) != 3:
        return 0
    
    # 使用列表存储转换结果
    values = [0, 0, 0]
    values.pop(0)
    values.insert(0, int(parts[0]))
    values.pop(1)
    values.insert(1, int(parts[1]))
    values.pop(2)
    values.insert(2, int(parts[2]))
    
    return values[0] * 3600 + values[1] * 60 + values[2]

# 方案5: 最简单的替换方案
def time_to_seconds_v5(time_str):
    """最简单的字符串替换方案"""
    if not time_str:
        return 0
    
    # 直接替换冒号为空格，然后分割
    time_str_spaces = time_str.replace(':', ' ')
    parts = time_str_spaces.split(' ')
    
    if len(parts) != 3:
        return 0
    
    h = int(parts[0])
    m = int(parts[1])
    s = int(parts[2])
    
    return h * 3600 + m * 60 + s

# 测试所有方案
def test_all_solutions():
    test_time = "09:03:35"
    expected = 32615
    
    solutions = [
        ("方案1-避免map", time_to_seconds_v1),
        ("方案2-字典存储", time_to_seconds_v2),
        ("方案3-手工解析", time_to_seconds_v3),
        ("方案4-列表操作", time_to_seconds_v4),
        ("方案5-字符串替换", time_to_seconds_v5),
    ]
    
    print(f"测试时间: {test_time}")
    print(f"期望结果: {expected}")
    print("-" * 40)
    
    for name, func in solutions:
        try:
            result = func(test_time)
            status = "✓" if result == expected else "✗"
            print(f"{status} {name}: {result}")
        except Exception as e:
            print(f"✗ {name}: 错误 - {e}")

if __name__ == "__main__":
    test_all_solutions()
    
    print("\n" + "="*50)
    print("推荐用于沙盒环境的版本:")
    print("="*50)
    
    # 推荐版本：方案5最简单
    def time_to_seconds_recommended(time_str):
        """推荐的沙盒环境版本"""
        if not time_str:
            return 0
        
        # 使用replace避免复杂的分割逻辑
        clean_str = time_str.replace(':', ' ')
        parts = clean_str.split()
        
        if len(parts) != 3:
            return 0
        
        try:
            h = int(parts[0])
            m = int(parts[1])
            s = int(parts[2])
            return h * 3600 + m * 60 + s
        except:
            return 0
    
    result = time_to_seconds_recommended("09:03:35")
    print(f"推荐版本结果: {result}")
    
    # 提供完整的替换代码
    print("\n完整的替换代码:")
    print("""
def time_to_seconds(time_str):
    if not time_str:
        return 0
    
    clean_str = time_str.replace(':', ' ')
    parts = clean_str.split()
    
    if len(parts) != 3:
        return 0
    
    try:
        h = int(parts[0])
        m = int(parts[1])
        s = int(parts[2])
        return h * 3600 + m * 60 + s
    except:
        return 0
""")
