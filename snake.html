<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snake Game</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #1a1a1a;
            font-family: 'Arial', sans-serif;
            background-image: radial-gradient(circle, #2a2a2a 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .game-title {
            color: #00ff00;
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #00ff00;
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from {
                text-shadow: 0 0 10px #00ff00;
            }
            to {
                text-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00;
            }
        }
        .instructions {
            color: #aaa;
            font-size: 16px;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .game-container {
            text-align: center;
            background: #222;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        canvas {
            border: 2px solid #444;
            background-color: #000;
            border-radius: 5px;
        }
        .score {
            color: #00ff00;
            font-size: 24px;
            margin-bottom: 20px;
            text-shadow: 0 0 5px #00ff00;
        }
        #gameOver {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            color: white;
            box-shadow: 0 0 20px rgba(255,0,0,0.5);
        }
        #gameOver h2 {
            color: #ff4444;
            font-size: 32px;
            margin-bottom: 20px;
            text-shadow: 0 0 5px #ff0000;
        }
        #gameOver button {
            background: #ff4444;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        #gameOver button:hover {
            background: #ff0000;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-title">Snake Game</div>
        <div class="instructions">
            Use arrow keys to control the snake<br>
            Eat the red food to grow longer<br>
            Avoid walls and your own tail!
        </div>
        <div class="score">Score: 0</div>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        let snake = [{x: 10, y: 10}];
        let food = {x: 5, y: 5};
        let direction = {x: 0, y: 0};
        let score = 0;
        let gameSpeed = 100;

        function gameLoop() {
            update();
            draw();
            if (!checkCollision()) {
                setTimeout(gameLoop, gameSpeed);
            }
        }

        function update() {
            const head = {x: snake[0].x + direction.x, y: snake[0].y + direction.y};
            
            // Check wall collision
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            // Check if snake eats food
            if (head.x === food.x && head.y === food.y) {
                score++;
                document.querySelector('.score').textContent = `Score: ${score}`;
                placeFood();
                gameSpeed = Math.max(50, gameSpeed - 5);
            } else {
                snake.pop();
            }

            snake.unshift(head);
        }

        function draw() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw snake
            ctx.fillStyle = 'lime';
            snake.forEach(part => {
                ctx.fillRect(part.x * gridSize, part.y * gridSize, gridSize, gridSize);
            });

            // Draw food
            ctx.fillStyle = 'red';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize, gridSize);
        }

        function placeFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // Make sure food doesn't appear on snake
            while (snake.some(part => part.x === food.x && part.y === food.y)) {
                food = {
                    x: Math.floor(Math.random() * tileCount),
                    y: Math.floor(Math.random() * tileCount)
                };
            }
        }

        function checkCollision() {
            // Check if snake hits itself
            for (let i = 1; i < snake.length; i++) {
                if (snake[i].x === snake[0].x && snake[i].y === snake[0].y) {
                    gameOver();
                    return true;
                }
            }
            return false;
        }

        function gameOver() {
            document.getElementById('gameOver').style.display = 'block';
            document.getElementById('finalScore').textContent = score;
        }

        function restartGame() {
            snake = [{x: 10, y: 10}];
            direction = {x: 0, y: 0};
            score = 0;
            gameSpeed = 100;
            document.querySelector('.score').textContent = `Score: ${score}`;
            document.getElementById('gameOver').style.display = 'none';
            placeFood();
            gameLoop();
        }

        function handleKeyPress(e) {
            switch(e.key) {
                case 'ArrowUp':
                    if (direction.y === 0) direction = {x: 0, y: -1};
                    break;
                case 'ArrowDown':
                    if (direction.y === 0) direction = {x: 0, y: 1};
                    break;
                case 'ArrowLeft':
                    if (direction.x === 0) direction = {x: -1, y: 0};
                    break;
                case 'ArrowRight':
                    if (direction.x === 0) direction = {x: 1, y: 0};
                    break;
            }
        }

        document.addEventListener('keydown', handleKeyPress);
        placeFood();
        gameLoop();
    </script>
</body>
</html>
