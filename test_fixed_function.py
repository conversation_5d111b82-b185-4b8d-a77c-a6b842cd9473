from fixed_find_mapping_value import find_mapping_value

# 测试函数
def test_fixed_function():
    # 模拟您提供的入参
    test_params = {
        "system_time": "09:03:35",
        "intentId": "PR-025",
        "history": "AI: 喂\n客户: 喂，你好。\nAI: 喂您好,请问是石秀增嘛?\n客户: 对。\nAI: 你的账户已经逾期，欠款210.87元，鉴于你的风险表现公司可能随时会升级处理。后续可能联系你的紧急联系人核实情况，请你今天必须拿出诚意来，2小时内把这个欠款还掉，把事情在内部解决掉好吧\n",
        "msg": "客户: 嗯，好的。",
        "extraInfo": {
            "request_time": "09:03:31"
        },
        "session": {
            "latest_request_time": "09:03:31",
            "start_request_time": ""
        },
        "script_intent_map": {}
    }
    
    # 调用修复后的函数
    result = find_mapping_value(**test_params)
    
    # 打印关键结果
    print("\n=== 测试结果 ===")
    print(f"request_duration: {result['request_duration']}")
    print(f"request_start_seconds: {result['request_start_seconds']}")
    print(f"request_end_seconds: {result['request_end_seconds']}")
    print(f"correctFlag: {result['correctFlag']}")
    print(f"reasoning: {result['reasoning']}")
    print(f"errType: {result['errType']}")
    print(f"exceptionType: {result['exceptionType']}")
    
    # 检查是否正确识别了超时
    if result['request_duration'] >= 3:
        print("\n✓ 成功识别超时问题")
    else:
        print("\n✗ 未能识别超时问题")

if __name__ == "__main__":
    test_fixed_function()
