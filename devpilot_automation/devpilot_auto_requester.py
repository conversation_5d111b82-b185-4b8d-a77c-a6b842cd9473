#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevPilot插件自动化请求工具
支持多种方式自动触发VSCode DevPilot插件的请求
"""

import os
import time
import json
import random
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import schedule

class DevPilotAutoRequester:
    def __init__(self, config_file: str = "devpilot_config.json"):
        """
        初始化DevPilot自动请求器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.setup_logging()
        self.request_count = 0
        self.weekly_limit = self.config.get('weekly_limit', 30)
        self.is_running = False
        
    def load_config(self) -> Dict:
        """加载配置文件"""
        default_config = {
            "weekly_limit": 30,
            "vscode_path": "code",  # VSCode命令路径
            "workspace_path": ".",  # 工作区路径
            "request_templates": [
                "解释这段代码的功能",
                "优化这个函数的性能",
                "添加错误处理",
                "生成单元测试",
                "重构这段代码",
                "添加注释说明",
                "检查潜在bug",
                "转换为TypeScript"
            ],
            "file_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".go"],
            "schedule_mode": "random",  # random, fixed, manual
            "daily_requests": 4,  # 每天请求次数
            "request_interval_hours": [2, 6],  # 请求间隔范围（小时）
            "working_hours": [9, 18],  # 工作时间范围
            "enabled": True
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    default_config.update(config)
            except Exception as e:
                logging.error(f"加载配置文件失败: {e}")
        else:
            self.save_config(default_config)
            
        return default_config
    
    def save_config(self, config: Dict = None):
        """保存配置文件"""
        if config is None:
            config = self.config
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('devpilot_automation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def get_code_files(self) -> List[str]:
        """获取工作区中的代码文件"""
        code_files = []
        workspace = self.config['workspace_path']
        extensions = self.config['file_extensions']
        
        for root, dirs, files in os.walk(workspace):
            # 跳过常见的忽略目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv']]
            
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    code_files.append(os.path.join(root, file))
                    
        return code_files
    
    def trigger_devpilot_request(self, file_path: str, request_text: str) -> bool:
        """
        触发DevPilot请求
        
        方法1: 通过VSCode命令行打开文件并模拟快捷键
        """
        try:
            # 打开VSCode并定位到文件
            cmd = [self.config['vscode_path'], file_path]
            subprocess.run(cmd, check=True)
            
            # 等待VSCode启动
            time.sleep(3)
            
            # 这里需要根据DevPilot的具体快捷键来调整
            # 通常AI插件的快捷键是Ctrl+Shift+P或者特定的快捷键
            logging.info(f"已打开文件: {file_path}")
            logging.info(f"请求内容: {request_text}")
            
            # 记录请求
            self.request_count += 1
            self.log_request(file_path, request_text)
            
            return True
            
        except Exception as e:
            logging.error(f"触发DevPilot请求失败: {e}")
            return False
    
    def trigger_devpilot_via_extension_api(self, request_text: str) -> bool:
        """
        方法2: 通过VSCode扩展API触发请求
        需要创建一个简单的VSCode扩展来接收外部请求
        """
        try:
            # 这里可以通过HTTP请求或者文件通信的方式
            # 与自定义的VSCode扩展进行通信
            api_data = {
                "command": "devpilot.request",
                "text": request_text,
                "timestamp": datetime.now().isoformat()
            }
            
            # 写入临时文件，让VSCode扩展监听
            temp_file = os.path.join(os.path.expanduser("~"), ".devpilot_request.json")
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(api_data, f, ensure_ascii=False)
            
            logging.info(f"已发送API请求: {request_text}")
            self.request_count += 1
            self.log_request("API", request_text)
            
            return True
            
        except Exception as e:
            logging.error(f"API请求失败: {e}")
            return False
    
    def log_request(self, file_path: str, request_text: str):
        """记录请求日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "file_path": file_path,
            "request_text": request_text,
            "request_count": self.request_count
        }
        
        log_file = "devpilot_requests.json"
        logs = []
        
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            except:
                logs = []
        
        logs.append(log_entry)
        
        # 只保留最近100条记录
        if len(logs) > 100:
            logs = logs[-100:]
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存请求日志失败: {e}")
    
    def make_random_request(self):
        """发起随机请求"""
        if self.request_count >= self.weekly_limit:
            logging.info(f"已达到周请求限制 ({self.weekly_limit})")
            return
        
        # 随机选择文件和请求模板
        code_files = self.get_code_files()
        if not code_files:
            logging.warning("未找到代码文件")
            return
        
        file_path = random.choice(code_files)
        request_text = random.choice(self.config['request_templates'])
        
        # 触发请求
        success = self.trigger_devpilot_request(file_path, request_text)
        if success:
            logging.info(f"成功发起请求 ({self.request_count}/{self.weekly_limit})")
        
    def schedule_requests(self):
        """安排请求计划"""
        if self.config['schedule_mode'] == 'random':
            # 随机时间安排
            daily_requests = self.config['daily_requests']
            working_hours = self.config['working_hours']
            
            for i in range(daily_requests):
                # 在工作时间内随机选择时间
                hour = random.randint(working_hours[0], working_hours[1])
                minute = random.randint(0, 59)
                schedule.every().day.at(f"{hour:02d}:{minute:02d}").do(self.make_random_request)
                
        elif self.config['schedule_mode'] == 'fixed':
            # 固定时间安排
            schedule.every().day.at("10:00").do(self.make_random_request)
            schedule.every().day.at("14:00").do(self.make_random_request)
            schedule.every().day.at("16:00").do(self.make_random_request)
            schedule.every().day.at("17:30").do(self.make_random_request)
    
    def start_automation(self):
        """启动自动化"""
        if not self.config['enabled']:
            logging.info("自动化已禁用")
            return
        
        self.is_running = True
        self.schedule_requests()
        
        logging.info("DevPilot自动化已启动")
        logging.info(f"周请求限制: {self.weekly_limit}")
        logging.info(f"当前请求计数: {self.request_count}")
        
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def stop_automation(self):
        """停止自动化"""
        self.is_running = False
        logging.info("DevPilot自动化已停止")
    
    def get_status(self) -> Dict:
        """获取状态信息"""
        return {
            "is_running": self.is_running,
            "request_count": self.request_count,
            "weekly_limit": self.weekly_limit,
            "remaining_requests": self.weekly_limit - self.request_count,
            "config": self.config
        }
    
    def reset_weekly_count(self):
        """重置周计数"""
        self.request_count = 0
        logging.info("周请求计数已重置")

def main():
    """主函数"""
    requester = DevPilotAutoRequester()
    
    try:
        # 启动自动化
        requester.start_automation()
    except KeyboardInterrupt:
        logging.info("收到中断信号，正在停止...")
        requester.stop_automation()

if __name__ == "__main__":
    main()
