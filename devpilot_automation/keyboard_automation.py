#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevPilot键盘自动化模块
通过模拟键盘操作来触发DevPilot插件
"""

import time
import platform
import subprocess
import logging
from typing import Optional

try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    logging.warning("pyautogui未安装，键盘自动化功能将受限")

try:
    if platform.system() == "Darwin":  # macOS
        import pynput
        PYNPUT_AVAILABLE = True
    else:
        PYNPUT_AVAILABLE = False
except ImportError:
    PYNPUT_AVAILABLE = False

class KeyboardAutomation:
    def __init__(self):
        self.system = platform.system()
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def is_vscode_active(self) -> bool:
        """检查VSCode是否为活动窗口"""
        try:
            if self.system == "Darwin":  # macOS
                script = '''
                tell application "System Events"
                    set frontApp to name of first application process whose frontmost is true
                    return frontApp
                end tell
                '''
                result = subprocess.run(['osascript', '-e', script], 
                                      capture_output=True, text=True)
                return "Visual Studio Code" in result.stdout
                
            elif self.system == "Windows":
                import win32gui
                window = win32gui.GetForegroundWindow()
                window_title = win32gui.GetWindowText(window)
                return "Visual Studio Code" in window_title
                
            elif self.system == "Linux":
                # Linux实现
                result = subprocess.run(['xdotool', 'getactivewindow', 'getwindowname'], 
                                      capture_output=True, text=True)
                return "Visual Studio Code" in result.stdout
                
        except Exception as e:
            self.logger.error(f"检查VSCode活动状态失败: {e}")
            return False
    
    def activate_vscode(self, workspace_path: Optional[str] = None) -> bool:
        """激活VSCode窗口"""
        try:
            if self.system == "Darwin":  # macOS
                if workspace_path:
                    subprocess.run(['open', '-a', 'Visual Studio Code', workspace_path])
                else:
                    subprocess.run(['open', '-a', 'Visual Studio Code'])
                    
            elif self.system == "Windows":
                if workspace_path:
                    subprocess.run(['code', workspace_path])
                else:
                    subprocess.run(['code'])
                    
            elif self.system == "Linux":
                if workspace_path:
                    subprocess.run(['code', workspace_path])
                else:
                    subprocess.run(['code'])
            
            # 等待VSCode启动
            time.sleep(3)
            return True
            
        except Exception as e:
            self.logger.error(f"激活VSCode失败: {e}")
            return False
    
    def send_keyboard_shortcut(self, shortcut: str) -> bool:
        """发送键盘快捷键"""
        try:
            if not PYAUTOGUI_AVAILABLE:
                self.logger.error("pyautogui未安装，无法发送键盘快捷键")
                return False
            
            # 确保VSCode是活动窗口
            if not self.is_vscode_active():
                self.logger.warning("VSCode不是活动窗口")
                return False
            
            # 解析快捷键
            keys = shortcut.lower().split('+')
            
            # 构建按键组合
            if self.system == "Darwin":  # macOS
                key_mapping = {
                    'cmd': 'command',
                    'ctrl': 'ctrl',
                    'shift': 'shift',
                    'alt': 'option'
                }
            else:  # Windows/Linux
                key_mapping = {
                    'cmd': 'ctrl',
                    'ctrl': 'ctrl',
                    'shift': 'shift',
                    'alt': 'alt'
                }
            
            # 转换按键
            converted_keys = []
            for key in keys:
                if key in key_mapping:
                    converted_keys.append(key_mapping[key])
                else:
                    converted_keys.append(key)
            
            # 发送快捷键
            pyautogui.hotkey(*converted_keys)
            self.logger.info(f"发送快捷键: {shortcut}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送快捷键失败: {e}")
            return False
    
    def type_text(self, text: str, interval: float = 0.05) -> bool:
        """输入文本"""
        try:
            if not PYAUTOGUI_AVAILABLE:
                self.logger.error("pyautogui未安装，无法输入文本")
                return False
            
            pyautogui.typewrite(text, interval=interval)
            self.logger.info(f"输入文本: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
            return False
    
    def trigger_devpilot_with_keyboard(self, request_text: str, file_path: Optional[str] = None) -> bool:
        """通过键盘操作触发DevPilot"""
        try:
            # 1. 激活VSCode
            if not self.activate_vscode():
                return False
            
            # 2. 如果指定了文件，打开文件
            if file_path:
                # Ctrl+O 打开文件
                if not self.send_keyboard_shortcut('ctrl+o'):
                    return False
                
                time.sleep(1)
                
                # 输入文件路径
                if not self.type_text(file_path):
                    return False
                
                # 按Enter确认
                pyautogui.press('enter')
                time.sleep(2)
            
            # 3. 选择一些代码（选择当前行）
            if not self.send_keyboard_shortcut('ctrl+l'):  # 选择当前行
                return False
            
            time.sleep(0.5)
            
            # 4. 尝试触发DevPilot
            # 方法1: 尝试常见的AI助手快捷键
            devpilot_shortcuts = [
                'ctrl+shift+p',  # 命令面板
                'ctrl+shift+i',  # 可能的DevPilot快捷键
                'alt+d',         # 可能的DevPilot快捷键
                'ctrl+alt+d',    # 可能的DevPilot快捷键
            ]
            
            for shortcut in devpilot_shortcuts:
                if self.send_keyboard_shortcut(shortcut):
                    time.sleep(1)
                    
                    # 如果是命令面板，搜索DevPilot
                    if shortcut == 'ctrl+shift+p':
                        if self.type_text('DevPilot'):
                            time.sleep(1)
                            pyautogui.press('enter')
                            time.sleep(1)
                            break
                    else:
                        # 直接触发了DevPilot
                        break
            
            # 5. 输入请求文本（如果DevPilot有输入框）
            time.sleep(1)
            if self.type_text(request_text):
                time.sleep(0.5)
                pyautogui.press('enter')
            
            self.logger.info(f"成功触发DevPilot请求: {request_text}")
            return True
            
        except Exception as e:
            self.logger.error(f"键盘自动化失败: {e}")
            return False
    
    def trigger_devpilot_via_command_palette(self, request_text: str) -> bool:
        """通过命令面板触发DevPilot"""
        try:
            # 1. 打开命令面板
            if not self.send_keyboard_shortcut('ctrl+shift+p'):
                return False
            
            time.sleep(1)
            
            # 2. 搜索DevPilot命令
            devpilot_commands = [
                'DevPilot: Chat',
                'DevPilot: Explain',
                'DevPilot: Optimize',
                'DevPilot: Generate',
                'DevPilot'
            ]
            
            for command in devpilot_commands:
                # 清空输入
                if self.send_keyboard_shortcut('ctrl+a'):
                    pyautogui.press('delete')
                
                # 输入命令
                if self.type_text(command):
                    time.sleep(1)
                    
                    # 按Enter执行
                    pyautogui.press('enter')
                    time.sleep(2)
                    
                    # 如果成功打开DevPilot，输入请求
                    if self.type_text(request_text):
                        time.sleep(0.5)
                        pyautogui.press('enter')
                        self.logger.info(f"通过命令面板触发DevPilot: {command}")
                        return True
            
            # 如果都失败了，按Escape关闭命令面板
            pyautogui.press('escape')
            return False
            
        except Exception as e:
            self.logger.error(f"命令面板自动化失败: {e}")
            return False

def test_keyboard_automation():
    """测试键盘自动化功能"""
    automation = KeyboardAutomation()
    
    # 测试基本功能
    print("测试VSCode激活...")
    if automation.activate_vscode():
        print("✓ VSCode激活成功")
    else:
        print("✗ VSCode激活失败")
        return
    
    time.sleep(2)
    
    print("测试VSCode活动状态检查...")
    if automation.is_vscode_active():
        print("✓ VSCode是活动窗口")
    else:
        print("✗ VSCode不是活动窗口")
    
    print("测试DevPilot触发...")
    if automation.trigger_devpilot_with_keyboard("解释这段代码的功能"):
        print("✓ DevPilot触发成功")
    else:
        print("✗ DevPilot触发失败")

if __name__ == "__main__":
    test_keyboard_automation()
