#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevPilot自动化GUI界面
提供可视化的配置和控制界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import threading
from datetime import datetime
try:
    from devpilot_auto_requester import DevPilotAutoRequester
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from devpilot_auto_requester import DevPilotAutoRequester

class DevPilotGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("DevPilot 自动化请求工具")
        self.root.geometry("800x600")
        
        self.requester = DevPilotAutoRequester()
        self.automation_thread = None
        
        self.setup_ui()
        self.update_status()
    
    def setup_ui(self):
        """设置UI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 状态信息区域
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="状态: 未启动")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.count_label = ttk.Label(status_frame, text="请求计数: 0/30")
        self.count_label.grid(row=1, column=0, sticky=tk.W)
        
        # 控制按钮区域
        control_frame = ttk.LabelFrame(main_frame, text="控制操作", padding="5")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="启动自动化", command=self.start_automation)
        self.start_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止自动化", command=self.stop_automation, state=tk.DISABLED)
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.manual_btn = ttk.Button(control_frame, text="手动请求", command=self.manual_request)
        self.manual_btn.grid(row=0, column=2, padx=(0, 5))
        
        self.reset_btn = ttk.Button(control_frame, text="重置计数", command=self.reset_count)
        self.reset_btn.grid(row=0, column=3)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="5")
        config_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 周请求限制
        ttk.Label(config_frame, text="周请求限制:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.weekly_limit_var = tk.StringVar(value=str(self.requester.config['weekly_limit']))
        weekly_limit_entry = ttk.Entry(config_frame, textvariable=self.weekly_limit_var, width=10)
        weekly_limit_entry.grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # 每日请求次数
        ttk.Label(config_frame, text="每日请求次数:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.daily_requests_var = tk.StringVar(value=str(self.requester.config['daily_requests']))
        daily_requests_entry = ttk.Entry(config_frame, textvariable=self.daily_requests_var, width=10)
        daily_requests_entry.grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # VSCode路径
        ttk.Label(config_frame, text="VSCode路径:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.vscode_path_var = tk.StringVar(value=self.requester.config['vscode_path'])
        vscode_path_entry = ttk.Entry(config_frame, textvariable=self.vscode_path_var)
        vscode_path_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(0, 5))
        
        # 工作区路径
        ttk.Label(config_frame, text="工作区路径:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.workspace_path_var = tk.StringVar(value=self.requester.config['workspace_path'])
        workspace_path_entry = ttk.Entry(config_frame, textvariable=self.workspace_path_var)
        workspace_path_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2, padx=(0, 5))
        
        ttk.Button(config_frame, text="选择", command=self.select_workspace).grid(row=3, column=2, pady=2)
        
        # 调度模式
        ttk.Label(config_frame, text="调度模式:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.schedule_mode_var = tk.StringVar(value=self.requester.config['schedule_mode'])
        schedule_mode_combo = ttk.Combobox(config_frame, textvariable=self.schedule_mode_var, 
                                         values=['random', 'fixed', 'manual'], state='readonly')
        schedule_mode_combo.grid(row=4, column=1, sticky=tk.W, pady=2)
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=5, column=0, columnspan=2, pady=10)
        
        # 请求模板区域
        template_frame = ttk.LabelFrame(main_frame, text="请求模板", padding="5")
        template_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        template_frame.columnconfigure(0, weight=1)
        template_frame.rowconfigure(0, weight=1)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(template_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.template_text = tk.Text(text_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.template_text.yview)
        self.template_text.configure(yscrollcommand=scrollbar.set)
        
        self.template_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 加载请求模板
        self.load_templates()
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_text_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(4, weight=1)
    
    def load_templates(self):
        """加载请求模板"""
        templates = self.requester.config['request_templates']
        self.template_text.delete(1.0, tk.END)
        self.template_text.insert(1.0, '\n'.join(templates))
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.requester.config['weekly_limit'] = int(self.weekly_limit_var.get())
            self.requester.config['daily_requests'] = int(self.daily_requests_var.get())
            self.requester.config['vscode_path'] = self.vscode_path_var.get()
            self.requester.config['workspace_path'] = self.workspace_path_var.get()
            self.requester.config['schedule_mode'] = self.schedule_mode_var.get()
            
            # 更新请求模板
            template_content = self.template_text.get(1.0, tk.END).strip()
            self.requester.config['request_templates'] = [t.strip() for t in template_content.split('\n') if t.strip()]
            
            # 保存配置文件
            self.requester.save_config()
            
            # 更新requester的weekly_limit
            self.requester.weekly_limit = self.requester.config['weekly_limit']
            
            messagebox.showinfo("成功", "配置已保存")
            self.log_message("配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", f"配置值无效: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def select_workspace(self):
        """选择工作区路径"""
        path = filedialog.askdirectory(title="选择工作区路径")
        if path:
            self.workspace_path_var.set(path)
    
    def start_automation(self):
        """启动自动化"""
        if self.automation_thread and self.automation_thread.is_alive():
            messagebox.showwarning("警告", "自动化已在运行中")
            return
        
        self.automation_thread = threading.Thread(target=self.requester.start_automation, daemon=True)
        self.automation_thread.start()
        
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        self.log_message("自动化已启动")
        self.update_status()
    
    def stop_automation(self):
        """停止自动化"""
        self.requester.stop_automation()
        
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        
        self.log_message("自动化已停止")
        self.update_status()
    
    def manual_request(self):
        """手动发起请求"""
        if self.requester.request_count >= self.requester.weekly_limit:
            messagebox.showwarning("警告", f"已达到周请求限制 ({self.requester.weekly_limit})")
            return
        
        # 在新线程中执行，避免阻塞UI
        threading.Thread(target=self.requester.make_random_request, daemon=True).start()
        self.log_message("手动请求已发起")
        
        # 延迟更新状态
        self.root.after(1000, self.update_status)
    
    def reset_count(self):
        """重置计数"""
        result = messagebox.askyesno("确认", "确定要重置周请求计数吗？")
        if result:
            self.requester.reset_weekly_count()
            self.log_message("周请求计数已重置")
            self.update_status()
    
    def update_status(self):
        """更新状态显示"""
        status = self.requester.get_status()
        
        status_text = "运行中" if status['is_running'] else "未启动"
        self.status_label.config(text=f"状态: {status_text}")
        
        count_text = f"请求计数: {status['request_count']}/{status['weekly_limit']}"
        self.count_label.config(text=count_text)
        
        # 定期更新状态
        self.root.after(5000, self.update_status)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

def main():
    root = tk.Tk()
    app = DevPilotGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
