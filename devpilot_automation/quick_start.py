#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevPilot自动化工具快速开始脚本
一键设置和启动自动化
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def create_default_config():
    """创建默认配置文件"""
    config = {
        "weekly_limit": 30,
        "vscode_path": "code",
        "workspace_path": os.getcwd(),
        "request_templates": [
            "解释这段代码的功能",
            "优化这个函数的性能", 
            "添加错误处理",
            "生成单元测试",
            "重构这段代码",
            "添加注释说明",
            "检查潜在bug",
            "转换为更好的实现方式"
        ],
        "file_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".go", ".rs"],
        "schedule_mode": "random",
        "daily_requests": 4,
        "request_interval_hours": [2, 6],
        "working_hours": [9, 18],
        "enabled": True
    }
    
    config_file = "devpilot_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已创建默认配置文件: {config_file}")
    return config

def install_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ Python依赖安装失败")
        return False

def setup_vscode_extension():
    """设置VSCode扩展"""
    extension_dir = Path("vscode_extension")
    
    if not extension_dir.exists():
        print("⚠️  VSCode扩展目录不存在，跳过扩展设置")
        return True
    
    print("🔧 设置VSCode扩展...")
    
    try:
        os.chdir(extension_dir)
        
        # 检查npm是否可用
        subprocess.run(['npm', '--version'], check=True, capture_output=True)
        
        # 安装依赖
        subprocess.run(['npm', 'install'], check=True)
        
        # 编译
        subprocess.run(['npm', 'run', 'compile'], check=True)
        
        print("✅ VSCode扩展编译完成")
        print("📝 请手动在VSCode中加载扩展:")
        print("   1. 打开VSCode")
        print("   2. 按F1 -> 'Extensions: Install from VSIX'")
        print(f"   3. 选择 {extension_dir.absolute()} 目录")
        
        os.chdir('..')
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  npm未安装或扩展编译失败，将使用其他触发方式")
        os.chdir('..')
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        from devpilot_auto_requester import DevPilotAutoRequester
        
        requester = DevPilotAutoRequester()
        status = requester.get_status()
        
        print(f"✅ 基本功能正常")
        print(f"   配置加载: ✓")
        print(f"   周限制: {status['weekly_limit']}")
        print(f"   工作区: {status['config']['workspace_path']}")
        
        # 测试代码文件发现
        code_files = requester.get_code_files()
        print(f"   发现代码文件: {len(code_files)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("🎉 DevPilot自动化工具设置完成！")
    print("="*60)
    
    print("\n📖 使用方法:")
    print("1. GUI界面启动:")
    print("   python start.py gui")
    print("   或直接运行: python devpilot_gui.py")
    
    print("\n2. 命令行启动:")
    print("   python start.py cli")
    print("   或直接运行: python devpilot_auto_requester.py")
    
    print("\n3. 测试键盘自动化:")
    print("   python start.py test")
    
    print("\n4. 查看状态:")
    print("   python start.py status")
    
    print("\n⚙️  配置文件: devpilot_config.json")
    print("📊 请求日志: devpilot_requests.json")
    print("📝 运行日志: devpilot_automation.log")
    
    print("\n💡 建议:")
    print("- 首次使用建议先用GUI界面进行配置")
    print("- 确保DevPilot插件已在VSCode中安装并启用")
    print("- 根据实际需要调整请求频率和模板")
    
    print("\n🔗 更多信息请查看 README.md")

def main():
    """主函数"""
    print("🚀 DevPilot自动化工具快速设置")
    print("="*50)
    
    # 1. 创建默认配置
    create_default_config()
    
    # 2. 安装依赖
    if not install_dependencies():
        print("⚠️  依赖安装失败，某些功能可能不可用")
    
    # 3. 设置VSCode扩展（可选）
    setup_vscode_extension()
    
    # 4. 测试基本功能
    if not test_basic_functionality():
        print("❌ 基本功能测试失败，请检查配置")
        return
    
    # 5. 显示使用指南
    show_usage_guide()
    
    # 6. 询问是否立即启动
    try:
        choice = input("\n是否立即启动GUI界面？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            print("🚀 启动GUI界面...")
            from devpilot_gui import main as gui_main
            gui_main()
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
