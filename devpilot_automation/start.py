#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevPilot自动化工具启动脚本
提供多种启动方式的选择
"""

import sys
import os
import subprocess
import argparse

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['schedule']
    optional_packages = {
        'pyautogui': '键盘自动化功能',
        'tkinter': 'GUI界面功能',
        'pynput': 'macOS键盘支持',
        'pywin32': 'Windows窗口操作'
    }
    
    missing_required = []
    missing_optional = []
    
    # 检查必需依赖
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_required.append(package)
    
    # 检查可选依赖
    for package, description in optional_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_optional.append((package, description))
    
    if missing_required:
        print("❌ 缺少必需依赖:")
        for package in missing_required:
            print(f"   - {package}")
        print("\n安装命令: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print("⚠️  缺少可选依赖:")
        for package, description in missing_optional:
            print(f"   - {package}: {description}")
        print("\n安装命令: pip install " + " ".join([p[0] for p in missing_optional]))
        print("注意: 可选依赖不影响基本功能，但会限制某些特性\n")
    
    print("✅ 依赖检查完成")
    return True

def start_gui():
    """启动GUI界面"""
    try:
        import tkinter
        from devpilot_gui import main
        print("🚀 启动GUI界面...")
        main()
    except ImportError:
        print("❌ tkinter未安装，无法启动GUI界面")
        print("请安装tkinter或使用命令行模式")
        return False
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False

def start_cli():
    """启动命令行模式"""
    try:
        from devpilot_auto_requester import main
        print("🚀 启动命令行自动化...")
        main()
    except Exception as e:
        print(f"❌ 命令行模式启动失败: {e}")
        return False

def test_keyboard():
    """测试键盘自动化"""
    try:
        from keyboard_automation import test_keyboard_automation
        print("🧪 测试键盘自动化功能...")
        test_keyboard_automation()
    except ImportError:
        print("❌ pyautogui未安装，无法测试键盘自动化")
        return False
    except Exception as e:
        print(f"❌ 键盘自动化测试失败: {e}")
        return False

def install_vscode_extension():
    """安装VSCode扩展"""
    extension_path = os.path.join(os.path.dirname(__file__), 'vscode_extension')
    
    if not os.path.exists(extension_path):
        print("❌ VSCode扩展源码不存在")
        return False
    
    try:
        print("📦 编译VSCode扩展...")
        os.chdir(extension_path)
        
        # 安装依赖
        subprocess.run(['npm', 'install'], check=True)
        
        # 编译
        subprocess.run(['npm', 'run', 'compile'], check=True)
        
        print("✅ VSCode扩展编译完成")
        print("请在VSCode中手动加载扩展:")
        print("1. 打开VSCode")
        print("2. 按F1打开命令面板")
        print("3. 输入'Extensions: Install from VSIX'")
        print(f"4. 选择 {extension_path} 目录")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 扩展编译失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ npm未安装，请先安装Node.js和npm")
        return False

def show_status():
    """显示当前状态"""
    try:
        from devpilot_auto_requester import DevPilotAutoRequester
        
        requester = DevPilotAutoRequester()
        status = requester.get_status()
        
        print("📊 当前状态:")
        print(f"   运行状态: {'运行中' if status['is_running'] else '未启动'}")
        print(f"   请求计数: {status['request_count']}/{status['weekly_limit']}")
        print(f"   剩余请求: {status['remaining_requests']}")
        print(f"   调度模式: {status['config']['schedule_mode']}")
        print(f"   每日请求: {status['config']['daily_requests']}")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='DevPilot自动化工具')
    parser.add_argument('mode', nargs='?', choices=['gui', 'cli', 'test', 'install', 'status'], 
                       default='gui', help='启动模式')
    parser.add_argument('--check-deps', action='store_true', help='检查依赖')
    
    args = parser.parse_args()
    
    print("🤖 DevPilot自动化工具")
    print("=" * 50)
    
    # 检查依赖
    if args.check_deps or args.mode != 'status':
        if not check_dependencies():
            sys.exit(1)
        print()
    
    # 根据模式启动
    if args.mode == 'gui':
        start_gui()
    elif args.mode == 'cli':
        start_cli()
    elif args.mode == 'test':
        test_keyboard()
    elif args.mode == 'install':
        install_vscode_extension()
    elif args.mode == 'status':
        show_status()

if __name__ == "__main__":
    main()
