{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAC7B,qCAAqC;AAQrC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,IAAI,OAAuC,CAAC;IAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,wBAAwB,CAAC,CAAC;IAErF,OAAO;IACP,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,sBAAsB,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACnG,iBAAiB,EAAE,CAAC;QACpB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kCAAkC,EAAE,GAAG,EAAE;QACjG,gBAAgB,EAAE,CAAC;QACnB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;IAEtF,SAAS;IACT,iBAAiB,EAAE,CAAC;IAEpB,SAAS,iBAAiB;QACtB,IAAI,OAAO,EAAE;YACT,OAAO;SACV;QAED,WAAW;QACX,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;YACjC,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,EAAE;YACtC,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC5B,IAAI;gBACA,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,OAAO,GAAsB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAEvD,IAAI,OAAO,CAAC,OAAO,KAAK,kBAAkB,IAAI,OAAO,CAAC,IAAI,EAAE;oBACxD,MAAM,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAE3C,SAAS;oBACT,EAAE,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;iBAC3C;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;aAChE;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,eAAe,CAAC,CAAC;IAC/D,CAAC;IAED,SAAS,gBAAgB;QACrB,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,OAAO,GAAG,SAAS,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;SACxC;IACL,CAAC;IAED,KAAK,UAAU,sBAAsB,CAAC,WAAmB;QACrD,IAAI;YACA,mCAAmC;YACnC,MAAM,gBAAgB,GAAG;gBACrB,eAAe;gBACf,kBAAkB;gBAClB,mBAAmB;gBACnB,mBAAmB;gBACnB,iBAAiB;aACpB,CAAC;YAEF,oBAAoB;YACpB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtF,IAAI,eAAe,EAAE;gBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;gBACnE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,WAAW,EAAE,CAAC,CAAC;gBAClF,OAAO;aACV;YAED,kBAAkB;YAClB,MAAM,2BAA2B,CAAC,WAAW,CAAC,CAAC;SAElD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;SAC1E;IACL,CAAC;IAED,KAAK,UAAU,2BAA2B,CAAC,WAAmB;QAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,cAAc;QACd,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QACzC,IAAI,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,CAAC,YAAY,EAAE;YACf,iBAAiB;YACjB,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACxE,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC;YAChC,YAAY,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACjG;QAED,4BAA4B;QAC5B,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;QAEtE,iBAAiB;QACjB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,mBAAmB;QACnB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QAEnE,SAAS;QACT,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,gBAAgB;QAChB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8CAA8C,CAAC,CAAC;QAErF,OAAO;QACP,OAAO,CAAC,GAAG,CAAC,6CAA6C,YAAY,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,YAAY,WAAW,EAAE,CAAC,CAAC;QAEvC,OAAO;QACP,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;IAChF,CAAC;IAED,OAAO;IACP,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;QACvB,OAAO,EAAE,GAAG,EAAE;YACV,gBAAgB,EAAE,CAAC;QACvB,CAAC;KACJ,CAAC,CAAC;AACP,CAAC;AAjJD,4BAiJC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;AACjE,CAAC;AAFD,gCAEC"}