"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const fs = require("fs");
const path = require("path");
const chokidar = require("chokidar");
function activate(context) {
    console.log('DevPilot Automation Bridge is now active!');
    let watcher;
    const requestFilePath = path.join(require('os').homedir(), '.devpilot_request.json');
    // 注册命令
    const triggerCommand = vscode.commands.registerCommand('devpilot-automation.triggerRequest', async () => {
        await triggerDevPilotRequest('手动触发的测试请求');
    });
    const startWatchingCommand = vscode.commands.registerCommand('devpilot-automation.startWatching', () => {
        startFileWatching();
        vscode.window.showInformationMessage('DevPilot automation watching started');
    });
    const stopWatchingCommand = vscode.commands.registerCommand('devpilot-automation.stopWatching', () => {
        stopFileWatching();
        vscode.window.showInformationMessage('DevPilot automation watching stopped');
    });
    context.subscriptions.push(triggerCommand, startWatchingCommand, stopWatchingCommand);
    // 自动开始监听
    startFileWatching();
    function startFileWatching() {
        if (watcher) {
            return;
        }
        // 确保请求文件存在
        if (!fs.existsSync(requestFilePath)) {
            fs.writeFileSync(requestFilePath, '{}');
        }
        watcher = chokidar.watch(requestFilePath, {
            persistent: true,
            ignoreInitial: true
        });
        watcher.on('change', async () => {
            try {
                const content = fs.readFileSync(requestFilePath, 'utf8');
                const request = JSON.parse(content);
                if (request.command === 'devpilot.request' && request.text) {
                    await triggerDevPilotRequest(request.text);
                    // 清空请求文件
                    fs.writeFileSync(requestFilePath, '{}');
                }
            }
            catch (error) {
                console.error('Error processing automation request:', error);
            }
        });
        console.log('File watching started for:', requestFilePath);
    }
    function stopFileWatching() {
        if (watcher) {
            watcher.close();
            watcher = undefined;
            console.log('File watching stopped');
        }
    }
    async function triggerDevPilotRequest(requestText) {
        try {
            // 方法1: 尝试直接调用DevPilot命令（如果知道具体命令名）
            const devpilotCommands = [
                'devpilot.chat',
                'devpilot.explain',
                'devpilot.optimize',
                'devpilot.generate',
                'devpilot.review'
            ];
            // 尝试找到并执行DevPilot命令
            const availableCommands = await vscode.commands.getCommands();
            const devpilotCommand = devpilotCommands.find(cmd => availableCommands.includes(cmd));
            if (devpilotCommand) {
                await vscode.commands.executeCommand(devpilotCommand, requestText);
                vscode.window.showInformationMessage(`DevPilot request executed: ${requestText}`);
                return;
            }
            // 方法2: 模拟用户输入和快捷键
            await simulateDevPilotInteraction(requestText);
        }
        catch (error) {
            console.error('Error triggering DevPilot request:', error);
            vscode.window.showErrorMessage(`Failed to trigger DevPilot: ${error}`);
        }
    }
    async function simulateDevPilotInteraction(requestText) {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        // 选择当前行或选中的文本
        const selection = activeEditor.selection;
        let selectedText = activeEditor.document.getText(selection);
        if (!selectedText) {
            // 如果没有选中文本，选择当前行
            const currentLine = activeEditor.document.lineAt(selection.active.line);
            selectedText = currentLine.text;
            activeEditor.selection = new vscode.Selection(currentLine.range.start, currentLine.range.end);
        }
        // 方法2a: 尝试打开命令面板并搜索DevPilot
        await vscode.commands.executeCommand('workbench.action.showCommands');
        // 等待一小段时间让命令面板打开
        await new Promise(resolve => setTimeout(resolve, 500));
        // 尝试输入DevPilot相关命令
        await vscode.commands.executeCommand('type', { text: 'DevPilot' });
        // 等待搜索结果
        await new Promise(resolve => setTimeout(resolve, 1000));
        // 按Enter选择第一个结果
        await vscode.commands.executeCommand('workbench.action.acceptSelectedQuickOpenItem');
        // 记录操作
        console.log(`Simulated DevPilot interaction with text: ${selectedText}`);
        console.log(`Request: ${requestText}`);
        // 显示通知
        vscode.window.showInformationMessage(`DevPilot automation: ${requestText}`);
    }
    // 清理函数
    context.subscriptions.push({
        dispose: () => {
            stopFileWatching();
        }
    });
}
exports.activate = activate;
function deactivate() {
    console.log('DevPilot Automation Bridge is now deactivated');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map