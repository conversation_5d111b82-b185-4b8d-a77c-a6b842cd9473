{"name": "devpilot-automation-bridge", "displayName": "DevPilot Automation Bridge", "description": "Bridge extension for DevPilot automation", "version": "1.0.0", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "devpilot-automation.triggerRequest", "title": "<PERSON>gger DevPilot Request"}, {"command": "devpilot-automation.startWatching", "title": "Start Automation Watching"}, {"command": "devpilot-automation.stopWatching", "title": "Stop Automation Watching"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"chokidar": "^3.5.3"}}