# DevPilot 自动化请求工具

这是一个专门为自研VSCode插件DevPilot设计的自动化请求工具，可以帮助您自动化地触发DevPilot插件的请求，满足每周30次的使用要求。

## 🚀 功能特性

- **自动化请求**: 按照设定的计划自动触发DevPilot请求
- **多种触发方式**: 支持文件监听、键盘模拟、VSCode扩展等多种方式
- **智能调度**: 支持随机时间、固定时间、手动触发等调度模式
- **可视化界面**: 提供友好的GUI界面进行配置和监控
- **请求记录**: 详细记录每次请求的时间和内容
- **灵活配置**: 支持自定义请求模板、工作时间、请求频率等

## 📦 安装依赖

```bash
# 基础依赖
pip install schedule pyautogui

# macOS额外依赖
pip install pynput

# Windows额外依赖（如果需要窗口操作）
pip install pywin32

# Linux额外依赖
sudo apt-get install xdotool  # Ubuntu/Debian
```

## 🛠️ 使用方法

### 方法1: GUI界面（推荐）

1. **启动GUI应用**:
```bash
python devpilot_gui.py
```

2. **配置设置**:
   - 设置周请求限制（默认30次）
   - 配置VSCode路径
   - 选择工作区路径
   - 设置调度模式和请求频率
   - 自定义请求模板

3. **启动自动化**:
   - 点击"启动自动化"按钮
   - 系统将按照设定的计划自动发起请求
   - 可以随时手动触发请求或停止自动化

### 方法2: 命令行方式

```bash
# 直接运行自动化
python devpilot_auto_requester.py

# 测试键盘自动化
python keyboard_automation.py
```

### 方法3: VSCode扩展桥接

1. **安装桥接扩展**:
```bash
cd vscode_extension
npm install
npm run compile
```

2. **在VSCode中加载扩展**:
   - 打开VSCode
   - 按F1打开命令面板
   - 输入"Extensions: Install from VSIX"
   - 选择编译后的扩展文件

3. **启动文件监听**:
   - 扩展会自动监听`~/.devpilot_request.json`文件
   - Python脚本通过写入此文件来触发DevPilot请求

## ⚙️ 配置说明

### 配置文件 (devpilot_config.json)

```json
{
  "weekly_limit": 30,
  "vscode_path": "code",
  "workspace_path": ".",
  "request_templates": [
    "解释这段代码的功能",
    "优化这个函数的性能",
    "添加错误处理",
    "生成单元测试",
    "重构这段代码",
    "添加注释说明",
    "检查潜在bug",
    "转换为TypeScript"
  ],
  "file_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".go"],
  "schedule_mode": "random",
  "daily_requests": 4,
  "request_interval_hours": [2, 6],
  "working_hours": [9, 18],
  "enabled": true
}
```

### 配置项说明

- `weekly_limit`: 每周请求次数限制
- `vscode_path`: VSCode可执行文件路径
- `workspace_path`: 工作区根目录路径
- `request_templates`: 请求模板列表
- `file_extensions`: 支持的代码文件扩展名
- `schedule_mode`: 调度模式（random/fixed/manual）
- `daily_requests`: 每日请求次数
- `request_interval_hours`: 请求间隔时间范围
- `working_hours`: 工作时间范围
- `enabled`: 是否启用自动化

## 🔧 触发方式详解

### 1. 键盘模拟方式

通过模拟键盘操作来触发DevPilot：

```python
from keyboard_automation import KeyboardAutomation

automation = KeyboardAutomation()
automation.trigger_devpilot_with_keyboard("解释这段代码")
```

**优点**: 最接近真实用户操作
**缺点**: 需要VSCode为活动窗口，可能被其他程序干扰

### 2. 文件监听方式

通过文件通信与VSCode扩展交互：

```python
# Python端写入请求
request_data = {
    "command": "devpilot.request",
    "text": "优化这个函数",
    "timestamp": datetime.now().isoformat()
}

with open("~/.devpilot_request.json", "w") as f:
    json.dump(request_data, f)
```

**优点**: 稳定可靠，不受窗口状态影响
**缺点**: 需要安装自定义VSCode扩展

### 3. 命令行方式

直接通过VSCode命令行参数：

```bash
code --command "devpilot.explain" --args "解释代码"
```

**优点**: 简单直接
**缺点**: 需要知道DevPilot的具体命令名称

## 📊 监控和日志

### 请求日志 (devpilot_requests.json)

记录每次请求的详细信息：

```json
[
  {
    "timestamp": "2024-01-15T10:30:00",
    "file_path": "/path/to/file.py",
    "request_text": "解释这段代码的功能",
    "request_count": 1
  }
]
```

### 运行日志 (devpilot_automation.log)

记录系统运行状态和错误信息。

## 🎯 最佳实践

1. **合理设置请求频率**: 建议每天4-5次，分散在工作时间内
2. **多样化请求内容**: 使用不同的请求模板，避免重复
3. **选择合适的文件**: 针对不同类型的代码文件发起请求
4. **监控请求计数**: 定期检查是否达到周限制
5. **备份配置**: 保存好配置文件，避免重复设置

## 🔍 故障排除

### 常见问题

1. **VSCode无法激活**:
   - 检查VSCode路径是否正确
   - 确保VSCode已正确安装
   - 尝试手动启动VSCode

2. **键盘模拟失败**:
   - 安装pyautogui: `pip install pyautogui`
   - 检查系统权限设置
   - 确保VSCode为活动窗口

3. **DevPilot无响应**:
   - 确认DevPilot插件已安装并启用
   - 检查插件的快捷键设置
   - 尝试手动触发确认插件工作正常

4. **请求计数不准确**:
   - 检查日志文件是否正常写入
   - 重置计数器: 在GUI中点击"重置计数"

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 自定义扩展

### 添加新的请求模板

编辑配置文件中的`request_templates`数组：

```json
"request_templates": [
  "你的自定义请求模板1",
  "你的自定义请求模板2"
]
```

### 支持新的文件类型

在`file_extensions`中添加新的扩展名：

```json
"file_extensions": [".py", ".js", ".ts", ".your_extension"]
```

### 自定义调度逻辑

继承`DevPilotAutoRequester`类并重写`schedule_requests`方法。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## ⚠️ 免责声明

本工具仅用于合理使用DevPilot插件，请遵守相关使用条款和限制。使用本工具产生的任何后果由用户自行承担。
