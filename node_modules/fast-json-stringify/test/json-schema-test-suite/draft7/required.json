[{"description": "required validation", "schema": {"properties": {"foo": {}, "bar": {}}, "required": ["foo"]}, "tests": [{"description": "present required property is valid", "data": {"foo": 1}, "valid": true}, {"description": "non-present required property is invalid", "data": {"bar": 1}, "valid": false}, {"description": "ignores arrays", "data": [], "valid": true}, {"description": "ignores strings", "data": "", "valid": true}, {"description": "ignores other non-objects", "data": 12, "valid": true}]}, {"description": "required default validation", "schema": {"properties": {"foo": {}}}, "tests": [{"description": "not required by default", "data": {}, "valid": true}]}, {"description": "required with empty array", "schema": {"properties": {"foo": {}}, "required": []}, "tests": [{"description": "property not required", "data": {}, "valid": true}]}]