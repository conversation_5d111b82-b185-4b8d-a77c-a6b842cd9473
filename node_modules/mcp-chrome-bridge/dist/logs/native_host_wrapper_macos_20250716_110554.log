--- Wrapper script called at Wed Jul 16 11:05:54 CST 2025 ---
SCRIPT_DIR: /Users/<USER>/Documents/work/cursor-workspace/node_modules/mcp-chrome-bridge/dist
LOG_DIR: /Users/<USER>/Documents/work/cursor-workspace/node_modules/mcp-chrome-bridge/dist/logs
NODE_SCRIPT: /Users/<USER>/Documents/work/cursor-workspace/node_modules/mcp-chrome-bridge/dist/index.js
Initial PATH: /usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/usr/local/mysql/bin://apache-maven-3.9.6/bin:/Library/Java/JavaVirtualMachines/temurin-21.jdk/Contents/Home/bin:/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-darwin-arm64/bundled/scripts/noConfigScripts
User: leiyu
Current PWD: /Users/<USER>/Documents/work/cursor-workspace
Searching for Node.js...
[Priority 1] Checking installation-time node path
Found installation-time node at /usr/local/bin/node
Using Node executable: /usr/local/bin/node
Node version: v22.16.0
Executing: /usr/local/bin/node /Users/<USER>/Documents/work/cursor-workspace/node_modules/mcp-chrome-bridge/dist/index.js
