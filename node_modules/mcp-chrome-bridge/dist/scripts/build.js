"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const distDir = path_1.default.join(__dirname, '..', '..', 'dist');
// 清理上次构建
console.log('清理上次构建...');
try {
    fs_1.default.rmSync(distDir, { recursive: true, force: true });
}
catch (err) {
    // 忽略目录不存在的错误
    console.log(err);
}
// 创建dist目录
fs_1.default.mkdirSync(distDir, { recursive: true });
fs_1.default.mkdirSync(path_1.default.join(distDir, 'logs'), { recursive: true }); // 创建logs目录
console.log('dist 和 dist/logs 目录已创建/确认存在');
// 编译TypeScript
console.log('编译TypeScript...');
(0, child_process_1.execSync)('tsc', { stdio: 'inherit' });
// 复制配置文件
console.log('复制配置文件...');
const configSourcePath = path_1.default.join(__dirname, '..', 'mcp', 'stdio-config.json');
const configDestPath = path_1.default.join(distDir, 'mcp', 'stdio-config.json');
try {
    // 确保目标目录存在
    fs_1.default.mkdirSync(path_1.default.dirname(configDestPath), { recursive: true });
    if (fs_1.default.existsSync(configSourcePath)) {
        fs_1.default.copyFileSync(configSourcePath, configDestPath);
        console.log(`已将 stdio-config.json 复制到 ${configDestPath}`);
    }
    else {
        console.error(`错误: 配置文件未找到: ${configSourcePath}`);
    }
}
catch (error) {
    console.error('复制配置文件时出错:', error);
}
// 复制package.json并更新其内容
console.log('准备package.json...');
const packageJson = require('../../package.json');
// 创建安装说明
const readmeContent = `# ${packageJson.name}

本程序为Chrome扩展的Native Messaging主机端。

## 安装说明

1. 确保已安装Node.js
2. 全局安装本程序:
   \`\`\`
   npm install -g ${packageJson.name}
   \`\`\`
3. 注册Native Messaging主机:
   \`\`\`
   # 用户级别安装（推荐）
   ${packageJson.name} register

   # 如果用户级别安装失败，可以尝试系统级别安装
   ${packageJson.name} register --system
   # 或者使用管理员权限
   sudo ${packageJson.name} register
   \`\`\`

## 使用方法

此应用程序由Chrome扩展自动启动，无需手动运行。
`;
fs_1.default.writeFileSync(path_1.default.join(distDir, 'README.md'), readmeContent);
console.log('复制包装脚本...');
const scriptsSourceDir = path_1.default.join(__dirname, '.');
const macOsWrapperSourcePath = path_1.default.join(scriptsSourceDir, 'run_host.sh');
const windowsWrapperSourcePath = path_1.default.join(scriptsSourceDir, 'run_host.bat');
const macOsWrapperDestPath = path_1.default.join(distDir, 'run_host.sh');
const windowsWrapperDestPath = path_1.default.join(distDir, 'run_host.bat');
try {
    if (fs_1.default.existsSync(macOsWrapperSourcePath)) {
        fs_1.default.copyFileSync(macOsWrapperSourcePath, macOsWrapperDestPath);
        console.log(`已将 ${macOsWrapperSourcePath} 复制到 ${macOsWrapperDestPath}`);
    }
    else {
        console.error(`错误: macOS 包装脚本源文件未找到: ${macOsWrapperSourcePath}`);
    }
    if (fs_1.default.existsSync(windowsWrapperSourcePath)) {
        fs_1.default.copyFileSync(windowsWrapperSourcePath, windowsWrapperDestPath);
        console.log(`已将 ${windowsWrapperSourcePath} 复制到 ${windowsWrapperDestPath}`);
    }
    else {
        console.error(`错误: Windows 包装脚本源文件未找到: ${windowsWrapperSourcePath}`);
    }
}
catch (error) {
    console.error('复制包装脚本时出错:', error);
}
// 为关键JavaScript文件和macOS包装脚本添加可执行权限
console.log('添加可执行权限...');
const filesToMakeExecutable = ['index.js', 'cli.js', 'run_host.sh']; // cli.js 假设在 dist 根目录
filesToMakeExecutable.forEach((file) => {
    const filePath = path_1.default.join(distDir, file); // filePath 现在是目标路径
    try {
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.chmodSync(filePath, '755');
            console.log(`已为 ${file} 添加可执行权限 (755)`);
        }
        else {
            console.warn(`警告: ${filePath} 不存在，无法添加可执行权限`);
        }
    }
    catch (error) {
        console.error(`为 ${file} 添加可执行权限时出错:`, error);
    }
});
console.log('✅ 构建完成');
//# sourceMappingURL=build.js.map