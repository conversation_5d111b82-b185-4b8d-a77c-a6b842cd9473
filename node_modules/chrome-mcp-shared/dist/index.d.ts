import { Tool } from '@modelcontextprotocol/sdk/types.js';

declare const DEFAULT_SERVER_PORT = 56889;
declare const HOST_NAME = "com.chrome_mcp.native_host";

declare enum NativeMessageType {
    START = "start",
    STARTED = "started",
    STOP = "stop",
    STOPPED = "stopped",
    PING = "ping",
    PONG = "pong",
    ERROR = "error",
    PROCESS_DATA = "process_data",
    PROCESS_DATA_RESPONSE = "process_data_response",
    CALL_TOOL = "call_tool",
    CALL_TOOL_RESPONSE = "call_tool_response",
    SERVER_STARTED = "server_started",
    SERVER_STOPPED = "server_stopped",
    ERROR_FROM_NATIVE_HOST = "error_from_native_host",
    CONNECT_NATIVE = "connectNative",
    PING_NATIVE = "ping_native",
    DISCONNECT_NATIVE = "disconnect_native"
}
interface NativeMessage<P = any, E = any> {
    type?: NativeMessageType;
    responseToRequestId?: string;
    payload?: P;
    error?: E;
}

declare const TOOL_NAMES: {
    BROWSER: {
        GET_WINDOWS_AND_TABS: string;
        SEARCH_TABS_CONTENT: string;
        NAVIGATE: string;
        SCREENSHOT: string;
        CLOSE_TABS: string;
        GO_BACK_OR_FORWARD: string;
        WEB_FETCHER: string;
        CLICK: string;
        FILL: string;
        GET_INTERACTIVE_ELEMENTS: string;
        NETWORK_CAPTURE_START: string;
        NETWORK_CAPTURE_STOP: string;
        NETWORK_REQUEST: string;
        NETWORK_DEBUGGER_START: string;
        NETWORK_DEBUGGER_STOP: string;
        KEYBOARD: string;
        HISTORY: string;
        BOOKMARK_SEARCH: string;
        BOOKMARK_ADD: string;
        BOOKMARK_DELETE: string;
        INJECT_SCRIPT: string;
        SEND_COMMAND_TO_INJECT_SCRIPT: string;
        CONSOLE: string;
    };
};
declare const TOOL_SCHEMAS: Tool[];

export { DEFAULT_SERVER_PORT, HOST_NAME, type NativeMessage, NativeMessageType, TOOL_NAMES, TOOL_SCHEMAS };
