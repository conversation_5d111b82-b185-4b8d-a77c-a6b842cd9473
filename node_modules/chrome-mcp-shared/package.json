{"name": "chrome-mcp-shared", "version": "1.0.1", "author": "hangye", "main": "dist/index.js", "module": "./dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "files": ["dist"], "devDependencies": {"@types/node": "^18.0.0", "@typescript-eslint/parser": "^8.32.0", "tsup": "^8.4.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "zod": "^3.24.4"}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --clean", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "npx eslint 'src/**/*.{js,ts}'", "lint:fix": "npx eslint 'src/**/*.{js,ts}' --fix", "format": "prettier --write 'src/**/*.{js,ts,json}'"}}