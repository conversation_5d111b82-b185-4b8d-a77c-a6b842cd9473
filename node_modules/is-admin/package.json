{"name": "is-admin", "version": "4.0.0", "description": "Check if the process is running as administrator on Windows", "license": "MIT", "repository": "sindresorhus/is-admin", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["admin", "administrator", "windows", "win32", "process", "running", "cmd", "shell", "command-line", "check", "detect", "is", "root"], "dependencies": {"execa": "^5.1.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}