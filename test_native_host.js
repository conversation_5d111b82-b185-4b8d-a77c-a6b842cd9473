#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// 测试Native Host通信
function testNativeHost() {
    const hostScript = path.join(__dirname, 'node_modules/mcp-chrome-bridge/dist/run_host.sh');
    
    console.log('启动Native Host:', hostScript);
    
    const host = spawn('bash', [hostScript], {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let responseReceived = false;
    
    // 监听输出
    host.stdout.on('data', (data) => {
        console.log('Host输出:', data.toString());
        responseReceived = true;
    });
    
    host.stderr.on('data', (data) => {
        console.error('Host错误:', data.toString());
    });
    
    host.on('close', (code) => {
        console.log('Host进程退出，代码:', code);
    });
    
    host.on('error', (error) => {
        console.error('Host启动错误:', error);
    });
    
    // 发送测试消息
    setTimeout(() => {
        const testMessage = {
            action: 'ping'
        };
        
        const messageStr = JSON.stringify(testMessage);
        const messageLength = Buffer.byteLength(messageStr, 'utf8');
        const lengthBuffer = Buffer.allocUnsafe(4);
        lengthBuffer.writeUInt32LE(messageLength, 0);
        
        console.log('发送测试消息:', testMessage);
        host.stdin.write(lengthBuffer);
        host.stdin.write(messageStr);
    }, 1000);
    
    // 超时检查
    setTimeout(() => {
        if (!responseReceived) {
            console.log('未收到响应，可能有问题');
        }
        host.kill();
    }, 5000);
}

testNativeHost();
