# -*- coding: utf-8 -*-
"""
测试Excel处理工具的修改功能
"""

import json
import pandas as pd
import tempfile
import os

def test_json_field_extraction():
    """测试JSON字段提取功能"""
    print("测试JSON字段提取功能...")
    
    # 测试数据
    test_json = '{"name": "张三", "age": 25, "city": "北京", "score": 95.5}'
    
    try:
        # 解析JSON
        data = json.loads(test_json)
        print(f"JSON解析成功: {data}")
        
        # 获取字段名
        fields = list(data.keys())
        print(f"字段名列表: {fields}")
        
        # 模拟字段选择
        selected_fields = ["name", "age", "city"]
        field_str = "|".join(selected_fields)
        print(f"选中字段字符串: {field_str}")
        
        # 模拟字段提取
        results = {}
        for field in selected_fields:
            if field in data:
                results[field] = str(data[field])
            else:
                results[field] = ""
        
        print(f"提取结果: {results}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_save_results_logic():
    """测试保存结果逻辑"""
    print("\n测试保存结果逻辑...")
    
    # 创建测试数据
    original_df = pd.DataFrame({
        'A': [1, 2, 3],
        'B': ['x', 'y', 'z'],
        'C': [10, 20, 30]
    })
    
    # 模拟处理后的数据（添加了结果列）
    result_df = original_df.copy()
    result_df['A_原样输出'] = result_df['A']  # 原样输出
    result_df['B_处理结果'] = ['X', 'Y', 'Z']  # 其他处理
    result_df['C_name'] = ['张三', '李四', '王五']  # 按字段取值
    result_df['C_age'] = [25, 30, 35]  # 按字段取值
    
    print("原始数据列:", list(original_df.columns))
    print("处理后数据列:", list(result_df.columns))
    
    # 模拟任务配置
    tasks = [
        {'column': 'A', 'method': '原样输出', 'config': ''},
        {'column': 'B', 'method': 'AI自定义任务', 'config': '转换为大写'},
        {'column': 'C', 'method': '按字段取值', 'config': 'name|age'}
    ]
    
    # 测试新建工作簿模式的列选择逻辑
    result_columns = [col for col in result_df.columns if col not in original_df.columns]
    print("结果列:", result_columns)
    
    # 找出原样输出的任务对应的原始列
    original_output_columns = []
    for task in tasks:
        if task['method'] == "原样输出":
            original_output_columns.append(task['column'])
    print("原样输出的原始列:", original_output_columns)
    
    # 合并需要保存的列
    columns_to_save = result_columns + original_output_columns
    columns_to_save = list(dict.fromkeys(columns_to_save))  # 去重
    print("新建工作簿模式下需要保存的列:", columns_to_save)
    
    # 验证结果
    expected_columns = ['A_原样输出', 'B_处理结果', 'C_name', 'C_age', 'A']
    if set(columns_to_save) == set(expected_columns):
        print("✅ 保存逻辑测试通过")
    else:
        print("❌ 保存逻辑测试失败")
        print(f"期望: {expected_columns}")
        print(f"实际: {columns_to_save}")

def test_invalid_json():
    """测试无效JSON的处理"""
    print("\n测试无效JSON处理...")
    
    invalid_jsons = [
        "这不是JSON",
        "123",
        "null",
        '{"incomplete": ',
        '["array", "not", "object"]'
    ]
    
    for test_data in invalid_jsons:
        try:
            if isinstance(test_data, str):
                data = json.loads(test_data)
            else:
                data = test_data
            
            if not isinstance(data, dict):
                raise ValueError("不是JSON对象格式")
                
            fields = list(data.keys())
            print(f"✅ {test_data} -> 字段: {fields}")
            
        except Exception as e:
            print(f"❌ {test_data} -> 错误: {str(e)}")

if __name__ == "__main__":
    test_json_field_extraction()
    test_save_results_logic()
    test_invalid_json()
    print("\n测试完成！")
