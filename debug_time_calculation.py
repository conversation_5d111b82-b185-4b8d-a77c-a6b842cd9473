# 调试时间计算问题
def debug_time_calculation():
    # 模拟您的入参
    system_time = "09:03:35"
    latest_request_time = "09:03:31"
    start_request_time = ""
    
    # 原始的时间转换函数
    def time_to_seconds(time_str):
        """将时间字符串转换为秒数"""
        if not time_str:
            return 0
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            return 0
        except:
            return 0
    
    # 测试时间转换
    print(f"system_time: {system_time}")
    print(f"latest_request_time: {latest_request_time}")
    print(f"start_request_time: '{start_request_time}'")
    
    request_start_seconds = time_to_seconds(latest_request_time)
    request_end_seconds = time_to_seconds(system_time)
    session_start_seconds = time_to_seconds(start_request_time)
    
    print(f"\ntime_to_seconds('{latest_request_time}') = {request_start_seconds}")
    print(f"time_to_seconds('{system_time}') = {request_end_seconds}")
    print(f"time_to_seconds('{start_request_time}') = {session_start_seconds}")
    
    request_duration = request_end_seconds - request_start_seconds
    print(f"\nrequest_duration = {request_end_seconds} - {request_start_seconds} = {request_duration}")
    
    # 分析问题
    print("\n=== 问题分析 ===")
    
    # 检查时间格式
    parts_latest = latest_request_time.split(':')
    parts_system = system_time.split(':')
    
    print(f"latest_request_time 分割结果: {parts_latest}, 长度: {len(parts_latest)}")
    print(f"system_time 分割结果: {parts_system}, 长度: {len(parts_system)}")
    
    # 检查是否是HH:MM:SS格式
    if len(parts_latest) == 3 and len(parts_system) == 3:
        print("时间格式正确，应该是3段式 HH:MM:SS")
        try:
            h1, m1, s1 = map(int, parts_latest)
            h2, m2, s2 = map(int, parts_system)
            print(f"latest_request_time 解析: {h1}时{m1}分{s1}秒")
            print(f"system_time 解析: {h2}时{m2}分{s2}秒")
            
            calc1 = h1 * 3600 + m1 * 60 + s1
            calc2 = h2 * 3600 + m2 * 60 + s2
            print(f"计算结果: {calc1}, {calc2}")
            print(f"差值: {calc2 - calc1}")
        except Exception as e:
            print(f"解析时间时出错: {e}")
    else:
        print("时间格式不正确，不是3段式")

if __name__ == "__main__":
    debug_time_calculation()
