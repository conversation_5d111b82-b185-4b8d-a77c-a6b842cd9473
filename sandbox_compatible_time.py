# 适配沙盒环境的时间处理函数

def time_to_seconds_sandbox(time_str):
    """
    适配沙盒环境的时间转换函数
    避免使用map()和复杂的异常处理
    """
    if not time_str:
        return 0
    
    # 手动分割和转换，避免使用map()
    parts = time_str.split(':')
    if len(parts) != 3:
        return 0
    
    # 逐个转换，避免map()函数
    try:
        hours = int(parts[0])
    except:
        return 0
    
    try:
        minutes = int(parts[1])
    except:
        return 0
    
    try:
        seconds = int(parts[2])
    except:
        return 0
    
    # 计算总秒数
    total_seconds = hours * 3600 + minutes * 60 + seconds
    return total_seconds

def time_to_seconds_alternative(time_str):
    """
    另一种更简单的实现方式
    """
    if not time_str:
        return 0
    
    parts = time_str.split(':')
    if len(parts) == 3:
        h = int(parts[0]) if parts[0].isdigit() else 0
        m = int(parts[1]) if parts[1].isdigit() else 0
        s = int(parts[2]) if parts[2].isdigit() else 0
        return h * 3600 + m * 60 + s
    
    return 0

def time_to_seconds_verbose(time_str):
    """
    详细调试版本
    """
    print(f"DEBUG: 输入时间字符串: '{time_str}'")
    
    if not time_str:
        print("DEBUG: 时间字符串为空")
        return 0
    
    parts = time_str.split(':')
    print(f"DEBUG: 分割后的部分: {parts}")
    print(f"DEBUG: 部分数量: {len(parts)}")
    
    if len(parts) != 3:
        print(f"DEBUG: 部分数量不等于3，返回0")
        return 0
    
    # 逐个转换并打印调试信息
    try:
        hours = int(parts[0])
        print(f"DEBUG: 小时转换成功: {hours}")
    except Exception as e:
        print(f"DEBUG: 小时转换失败: {e}")
        return 0
    
    try:
        minutes = int(parts[1])
        print(f"DEBUG: 分钟转换成功: {minutes}")
    except Exception as e:
        print(f"DEBUG: 分钟转换失败: {e}")
        return 0
    
    try:
        seconds = int(parts[2])
        print(f"DEBUG: 秒转换成功: {seconds}")
    except Exception as e:
        print(f"DEBUG: 秒转换失败: {e}")
        return 0
    
    # 计算总秒数
    total_seconds = hours * 3600 + minutes * 60 + seconds
    print(f"DEBUG: 计算结果: {hours}*3600 + {minutes}*60 + {seconds} = {total_seconds}")
    
    return total_seconds

# 测试所有版本
if __name__ == "__main__":
    test_time = "09:03:35"
    
    print("=== 测试原始版本 ===")
    def time_to_seconds_original(time_str):
        if not time_str:
            return 0
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            return 0
        except:
            return 0
    
    result_original = time_to_seconds_original(test_time)
    print(f"原始版本结果: {result_original}")
    
    print("\n=== 测试沙盒兼容版本 ===")
    result_sandbox = time_to_seconds_sandbox(test_time)
    print(f"沙盒兼容版本结果: {result_sandbox}")
    
    print("\n=== 测试替代版本 ===")
    result_alternative = time_to_seconds_alternative(test_time)
    print(f"替代版本结果: {result_alternative}")
    
    print("\n=== 测试详细调试版本 ===")
    result_verbose = time_to_seconds_verbose(test_time)
    print(f"详细调试版本结果: {result_verbose}")
