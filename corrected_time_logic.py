# 修正后的时间处理逻辑
def get_corrected_time_values(system_time, extraInfo, session):
    """
    修正后的时间获取逻辑
    """
    # 优先从session获取，如果为空则从extraInfo获取
    latest_request_time = session.get('latest_request_time') or extraInfo.get('request_time', '')
    start_request_time = session.get('start_request_time', '')
    
    # 如果start_request_time为空，使用latest_request_time作为fallback
    if not start_request_time:
        start_request_time = latest_request_time
    
    print(f"修正后的时间值:")
    print(f"  latest_request_time: '{latest_request_time}'")
    print(f"  start_request_time: '{start_request_time}'")
    print(f"  system_time: '{system_time}'")
    
    return latest_request_time, start_request_time

# 测试修正后的逻辑
if __name__ == "__main__":
    # 您的入参
    system_time = "09:03:35"
    extraInfo = {"request_time": "09:03:31"}
    session = {
        "latest_request_time": "09:03:31",
        "start_request_time": ""
    }
    
    latest_request_time, start_request_time = get_corrected_time_values(
        system_time, extraInfo, session
    )
    
    # 时间转换函数
    def time_to_seconds(time_str):
        if not time_str:
            return 0
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            return 0
        except:
            return 0
    
    # 计算时间差
    request_start_seconds = time_to_seconds(latest_request_time)
    request_end_seconds = time_to_seconds(system_time)
    request_duration = request_end_seconds - request_start_seconds
    
    print(f"\n计算结果:")
    print(f"  request_start_seconds: {request_start_seconds}")
    print(f"  request_end_seconds: {request_end_seconds}")
    print(f"  request_duration: {request_duration} 秒")
    
    if request_duration >= 3:
        print(f"  ✓ 检测到超时: {request_duration}秒 >= 3秒")
    else:
        print(f"  ✗ 未超时: {request_duration}秒 < 3秒")
